[2025-09-05 21:32:41] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:32:41] INFO     Gemini AI client initialized successfully
[2025-09-05 21:32:41] INFO     🤖 Testing AI processor connection...
[2025-09-05 21:32:43] ERROR    Gemini API connection test failed: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:32:43] WARNING  AI processor test failed. License plate detection may not work.
[2025-09-05 21:32:43] INFO     📷 Testing camera connection...
[2025-09-05 21:32:43] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 21:32:43] ERROR    RTSP URL not configured
[2025-09-05 21:32:43] WARNING  ❌ Real camera connection failed after all attempts
[2025-09-05 21:32:43] INFO     🎭 Switching to mock camera for demonstration
[2025-09-05 21:32:43] INFO     Connecting to mock camera...
[2025-09-05 21:32:44] INFO     Mock camera connected successfully
[2025-09-05 21:32:44] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 21:32:58] INFO     Received signal 2, shutting down gracefully...
[2025-09-05 21:32:58] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:32:58] INFO     Disconnecting from mock camera
[2025-09-05 21:32:58] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:32:59] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:32:59] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:41:56] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:41:57] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:41:57] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:41:57] INFO     🎭 Using mock camera for testing
[2025-09-05 21:41:57] INFO     Gemini AI client initialized successfully
[2025-09-05 21:41:57] INFO     Connecting to mock camera...
[2025-09-05 21:41:58] INFO     Mock camera connected successfully
[2025-09-05 21:42:23] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:42:23] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:42:23] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:42:23] INFO     🎭 Using mock camera for testing
[2025-09-05 21:42:23] INFO     Gemini AI client initialized successfully
[2025-09-05 21:42:23] INFO     Connecting to mock camera...
[2025-09-05 21:42:24] INFO     Mock camera connected successfully
[2025-09-05 21:42:24] INFO     Generated mock image without license plate
[2025-09-05 21:42:24] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_214224.jpg
[2025-09-05 21:42:24] INFO     Processing image for Object Detection: captured_images/mock_capture_20250905_214224.jpg
[2025-09-05 21:42:26] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Empty road with white dashed lines under a light blue sky.', 'word_count': 11, 'raw_response': 'Empty road with white dashed lines under a light blue sky.', 'image_path': 'captured_images/mock_capture_20250905_214224.jpg'}
[2025-09-05 21:42:33] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:42:34] INFO     🔄 Mode changed to: license_plate
[2025-09-05 21:42:34] INFO     🎯 Operating in License Plate Detection mode
[2025-09-05 21:42:34] INFO     🎭 Using mock camera for testing
[2025-09-05 21:42:34] INFO     Gemini AI client initialized successfully
[2025-09-05 21:42:34] INFO     Connecting to mock camera...
[2025-09-05 21:42:35] INFO     Mock camera connected successfully
[2025-09-05 21:42:35] INFO     Generated mock image with license plate: MNO678
[2025-09-05 21:42:35] INFO     Mock frame captured and saved: captured_images/mock_capture_20250905_214235.jpg
[2025-09-05 21:42:35] INFO     Processing image for License Plate Detection: captured_images/mock_capture_20250905_214235.jpg
[2025-09-05 21:42:37] INFO     API call successful: {'success': True, 'license_plates': [{'plate_number': 'MNO678', 'location': 'On the rear of a blue rectangular object (simulating a vehicle) on the road.', 'confidence': 'High', 'details': 'The license plate is white text on a dark blue background within a blue rectangular frame.'}], 'plates_detected': 1, 'raw_response': 'LICENSE PLATES DETECTED: 1\n\nPlate 1:\n- Number: MNO678\n- Location: On the rear of a blue rectangular object (simulating a vehicle) on the road.\n- Confidence: High\n- Details: The license plate is white text on a dark blue background within a blue rectangular frame.', 'image_path': 'captured_images/mock_capture_20250905_214235.jpg'}
[2025-09-05 21:42:37] INFO     License plates detected in captured_images/mock_capture_20250905_214235.jpg: [{'plate_number': 'MNO678', 'location': 'On the rear of a blue rectangular object (simulating a vehicle) on the road.', 'confidence': 'High', 'details': 'The license plate is white text on a dark blue background within a blue rectangular frame.'}]
[2025-09-05 21:43:28] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:43:29] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:43:29] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:43:29] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:43:29] INFO     Gemini AI client initialized successfully
[2025-09-05 21:43:29] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@192.168.68.63:1935
[2025-09-05 21:43:29] WARNING  ⚠️ Failed to open RTSP stream (attempt 1)
[2025-09-05 21:43:29] INFO     🔄 Retry 2/3 in 2 seconds...
[2025-09-05 21:43:31] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@192.168.68.63:1935
[2025-09-05 21:43:31] WARNING  ⚠️ Failed to open RTSP stream (attempt 2)
[2025-09-05 21:43:31] INFO     🔄 Retry 3/3 in 4 seconds...
[2025-09-05 21:43:35] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@***:***@192.168.68.63:1935
[2025-09-05 21:43:35] WARNING  ⚠️ Failed to open RTSP stream (attempt 3)
[2025-09-05 21:43:35] ERROR    ❌ Failed to connect to RTSP camera after 3 attempts
[2025-09-05 21:44:09] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:44:09] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:44:09] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:44:09] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:44:09] INFO     Gemini AI client initialized successfully
[2025-09-05 21:44:09] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@192.168.68.63:1935
[2025-09-05 21:44:13] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:44:13] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:44:13] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:13] INFO     Image captured successfully: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:13] INFO     Processing image for Object Detection: captured_images/capture_20250905_214413.jpg
[2025-09-05 21:44:14] INFO     API call successful: {'success': True, 'mode': 'object_detection', 'description': 'Person looking down near large illuminated number three sculpture.', 'word_count': 9, 'raw_response': 'Person looking down near large illuminated number three sculpture.', 'image_path': 'captured_images/capture_20250905_214413.jpg'}
[2025-09-05 21:44:28] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:44:29] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:44:29] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:44:29] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:44:29] INFO     Gemini AI client initialized successfully
[2025-09-05 21:44:29] INFO     🤖 Testing AI processor connection...
[2025-09-05 21:44:30] INFO     Gemini API connection test successful. Response: API connection successful...
[2025-09-05 21:44:30] INFO     📷 Testing camera connection...
[2025-09-05 21:44:30] INFO     🔄 Attempting to connect to real RTSP camera...
[2025-09-05 21:44:30] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@192.168.68.63:1935
[2025-09-05 21:44:33] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:44:33] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:44:33] INFO     ✅ Real RTSP camera connected successfully!
[2025-09-05 21:44:33] INFO     ⏰ Scheduling capture every 1 minute(s)
[2025-09-05 21:44:38] INFO     Received signal 15, shutting down gracefully...
[2025-09-05 21:44:38] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:44:38] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 21:44:38] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:44:39] INFO     🛑 Stopping License Plate Monitoring System...
[2025-09-05 21:44:39] INFO     🔌 Disconnected from RTSP camera
[2025-09-05 21:44:39] INFO     ✅ License Plate Monitoring System stopped
[2025-09-05 21:45:40] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:45:40] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:45:40] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:45:40] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:45:40] INFO     Gemini AI client initialized successfully
[2025-09-05 21:45:40] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@192.168.68.63:1935
[2025-09-05 21:45:43] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:45:43] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:45:43] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] INFO     Image captured successfully: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] INFO     Processing image for Object Detection: captured_images/capture_20250905_214543.jpg
[2025-09-05 21:45:43] ERROR    Error processing image captured_images/capture_20250905_214543.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:45:43] ERROR    API call failed: Error processing image captured_images/capture_20250905_214543.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:47:35] INFO     ✅ Configuration loaded from config.json
[2025-09-05 21:47:36] INFO     🔄 Mode changed to: object_detection
[2025-09-05 21:47:36] INFO     🎯 Operating in Object Detection mode
[2025-09-05 21:47:36] INFO     📹 Initializing real RTSP camera
[2025-09-05 21:47:36] INFO     Gemini AI client initialized successfully
[2025-09-05 21:47:36] INFO     🔌 Connecting to RTSP camera: rtsp://***:***@192.168.68.63:1935
[2025-09-05 21:47:39] INFO     ✅ Successfully connected to RTSP camera (retry 1, 2 successful reads)
[2025-09-05 21:47:39] INFO     📐 Frame size: (480, 640, 3)
[2025-09-05 21:47:39] INFO     ✅ Image captured and saved: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:39] INFO     Image captured successfully: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:39] INFO     Processing image for Object Detection: captured_images/capture_20250905_214739.jpg
[2025-09-05 21:47:41] ERROR    Error processing image captured_images/capture_20250905_214739.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
[2025-09-05 21:47:41] ERROR    API call failed: Error processing image captured_images/capture_20250905_214739.jpg: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
